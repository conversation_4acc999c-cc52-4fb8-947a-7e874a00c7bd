# Manual Avatar Update Test

This document outlines the manual testing steps to verify the avatar update functionality works correctly.

## Test Steps

### 1. Avatar Display Test
- [ ] Open the Edit Profile screen
- [ ] Verify that the current avatar is displayed in a circular container
- [ ] Verify that a camera icon is visible at the bottom-right of the avatar
- [ ] Verify that the avatar has proper border styling matching the Figma design

### 2. Avatar Selection Bottom Sheet Test
- [ ] Tap the camera icon on the avatar
- [ ] Verify that a bottom sheet appears with three options:
  - [ ] "Upload" with picture icon
  - [ ] "Take Photo" with camera icon  
  - [ ] "Generate with AI" with AI icon
- [ ] Verify the bottom sheet has proper styling with drag indicator

### 3. Gallery Selection Test
- [ ] Tap "Upload" option in the bottom sheet
- [ ] Verify that the device's photo gallery opens
- [ ] Select an image from the gallery
- [ ] Verify that the avatar updates to show the selected image
- [ ] Verify that a success message is displayed

### 4. Camera Selection Test
- [ ] Tap the camera icon again to open bottom sheet
- [ ] Tap "Take Photo" option
- [ ] Verify that the device's camera opens
- [ ] Take a photo
- [ ] Verify that the avatar updates to show the captured image
- [ ] Verify that a success message is displayed

### 5. AI Generate Test (Coming Soon)
- [ ] Tap the camera icon again to open bottom sheet
- [ ] Tap "Generate with AI" option
- [ ] Verify that a "Coming soon" message is displayed
- [ ] Verify that the bottom sheet closes

### 6. Error Handling Test
- [ ] Try to access camera/gallery without permissions
- [ ] Verify that appropriate error messages are displayed
- [ ] Cancel image selection process
- [ ] Verify that no error occurs and avatar remains unchanged

### 7. UI Component Integration Test
- [ ] Verify that the extracted widgets work correctly:
  - [ ] AvatarSectionWidget displays properly
  - [ ] ProfileFormFieldsWidget displays form fields
  - [ ] AvatarSelectionBottomSheet shows options correctly
- [ ] Verify that the main EditProfileScreen uses the extracted components
- [ ] Verify that the code is properly organized and maintainable

## Expected Results

✅ All avatar functionality should work as designed
✅ UI components should be properly extracted and reusable
✅ Error handling should be robust
✅ User experience should be smooth and intuitive
✅ Code should follow the project's architecture patterns

## Notes

- The avatar upload functionality currently uses local file paths
- In a production environment, images would be uploaded to a server
- The AvatarUploadCubit has been created for future server integration
- All UI components follow the app's design system and color scheme
