import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:toii_social/cubit/user/edit_profile/edit_profile_cubit.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/avatar_section_widget.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/avatar_selection_bottom_sheet.dart';
import 'package:toii_social/screen/user_profile/edit_profile/widgets/profile_form_fields_widget.dart';

void main() {
  group('EditProfile Widgets', () {
    late UserModel testUser;

    setUp(() {
      testUser = UserModel(
        id: '1',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        fullName: 'Test User',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
        bio: 'Test bio',
      );
    });

    testWidgets('AvatarSectionWidget should display avatar and camera icon', (
      tester,
    ) async {
      const state = EditProfileState(
        status: EditProfileStatus.initial,
        displayName: 'Test User',
        bio: 'Test bio',
        avatar: 'https://example.com/avatar.jpg',
        hasChanges: false,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AvatarSectionWidget(
              state: state,
              user: testUser,
              onAvatarTap: () {},
            ),
          ),
        ),
      );

      // Verify camera icon for avatar editing is present
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);

      // Verify avatar container is present
      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    testWidgets('ProfileFormFieldsWidget should display form fields', (
      tester,
    ) async {
      const state = EditProfileState(
        status: EditProfileStatus.initial,
        displayName: 'Test User',
        bio: 'Test bio',
        avatar: 'https://example.com/avatar.jpg',
        hasChanges: false,
      );

      final displayNameController = TextEditingController(text: 'Test User');
      final bioController = TextEditingController(text: 'Test bio');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProfileFormFieldsWidget(
              state: state,
              user: testUser,
              displayNameController: displayNameController,
              bioController: bioController,
              onDisplayNameChanged: (value) {},
              onBioChanged: (value) {},
            ),
          ),
        ),
      );

      // Verify field labels are present
      expect(find.text('Display Name'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Bio'), findsOneWidget);

      // Verify text fields are present
      expect(find.byType(TextField), findsAtLeastNWidgets(2));
    });

    testWidgets('AvatarSelectionBottomSheet should display options', (
      tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed: () {
                      AvatarSelectionBottomSheet.show(
                        context: context,
                        onGalleryTap: () {},
                        onCameraTap: () {},
                        onAIGenerateTap: () {},
                      );
                    },
                    child: const Text('Show Bottom Sheet'),
                  ),
            ),
          ),
        ),
      );

      // Tap button to show bottom sheet
      await tester.tap(find.text('Show Bottom Sheet'));
      await tester.pumpAndSettle();

      // Verify bottom sheet options are displayed
      expect(find.text('Upload'), findsOneWidget);
      expect(find.text('Take Photo'), findsOneWidget);
      expect(find.text('Generate with AI'), findsOneWidget);
    });
  });
}
