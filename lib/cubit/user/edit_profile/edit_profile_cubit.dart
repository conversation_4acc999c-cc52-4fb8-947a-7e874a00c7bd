import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'edit_profile_state.dart';

class EditProfileCubit extends Cubit<EditProfileState> {
  EditProfileCubit() : super(const EditProfileState());

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  String? _originalDisplayName;
  String? _originalBio;
  String? _originalAvatar;

  void initializeProfile(UserModel user) {
    _originalDisplayName = user.fullName ?? user.firstName;
    _originalBio = user.bio;
    _originalAvatar = user.avatar;

    emit(
      state.copyWith(
        displayName: _originalDisplayName,
        bio: _originalBio,
        avatar: _originalAvatar,
        hasChanges: false,
      ),
    );
  }

  void updateDisplayName(String displayName) {
    final hasChanges = _hasChanges(
      displayName: displayName,
      bio: state.bio,
      avatar: state.avatar,
    );

    emit(state.copyWith(displayName: displayName, hasChanges: hasChanges));
  }

  void updateBio(String bio) {
    final hasChanges = _hasChanges(
      displayName: state.displayName,
      bio: bio,
      avatar: state.avatar,
    );

    emit(state.copyWith(bio: bio, hasChanges: hasChanges));
  }

  void updateAvatar(String avatar) {
    final hasChanges = _hasChanges(
      displayName: state.displayName,
      bio: state.bio,
      avatar: avatar,
    );

    emit(state.copyWith(avatar: avatar, hasChanges: hasChanges));
  }

  bool _hasChanges({String? displayName, String? bio, String? avatar}) {
    return displayName != _originalDisplayName ||
        bio != _originalBio ||
        avatar != _originalAvatar;
  }

  Future<void> saveProfile(String userId) async {
    if (!state.hasChanges) return;

    emit(state.copyWith(status: EditProfileStatus.loading));

    try {
      final request = UpdateUserRequestModel(
        fullName: state.displayName,
        bio: state.bio,
        avatar: state.avatar,
      );

      final result = await _userRepository.updateUser(userId, request);

      // Update original values after successful save
      _originalDisplayName = state.displayName;
      _originalBio = state.bio;
      _originalAvatar = state.avatar;

      emit(
        state.copyWith(status: EditProfileStatus.success, hasChanges: false),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: e.message ?? 'Network error occurred',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: EditProfileStatus.failure,
          errorMessage: 'An unexpected error occurred',
        ),
      );
    }
  }

  void resetState() {
    emit(const EditProfileState());
  }
}
