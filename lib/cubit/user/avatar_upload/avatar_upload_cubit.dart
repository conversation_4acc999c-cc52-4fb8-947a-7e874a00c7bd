import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_social/core/repository/user_repository.dart';

part 'avatar_upload_state.dart';

class AvatarUploadCubit extends Cubit<AvatarUploadState> {
  AvatarUploadCubit() : super(const AvatarUploadState());

  final ImagePicker _imagePicker = ImagePicker();
  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  /// Pick image from gallery
  Future<void> pickImageFromGallery() async {
    try {
      emit(state.copyWith(status: AvatarUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.success,
            localImagePath: image.path,
            errorMessage: null,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: 'Error selecting image: ${e.toString()}',
        ),
      );
    }
  }

  /// Pick image from camera
  Future<void> pickImageFromCamera() async {
    try {
      emit(state.copyWith(status: AvatarUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.success,
            localImagePath: image.path,
            errorMessage: null,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: AvatarUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: 'Error taking photo: ${e.toString()}',
        ),
      );
    }
  }

  /// Upload avatar to server using UserRepository
  Future<void> uploadAvatar() async {
    if (state.localImagePath == null) {
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: 'No image selected',
        ),
      );
      return;
    }

    try {
      emit(
        state.copyWith(status: AvatarUploadStatus.loading, uploadProgress: 0.0),
      );

      final file = File(state.localImagePath!);

      // Use UserRepository's uploadAvatar method
      await _userRepository.uploadAvatar(file);

      // If upload successful, ProfileCubit.getProfile() is automatically called by UserRepository
      emit(
        state.copyWith(
          status: AvatarUploadStatus.success,
          uploadedImageUrl:
              state
                  .localImagePath!, // Temporary until we get the actual URL from server
          uploadProgress: 1.0,
          errorMessage: null,
        ),
      );
    } catch (e) {
      // Exception is thrown by UserRepository if upload fails
      emit(
        state.copyWith(
          status: AvatarUploadStatus.failure,
          errorMessage: e.toString(),
          uploadProgress: 0.0,
        ),
      );
    }
  }

  /// Clear the current avatar selection
  void clearAvatar() {
    emit(const AvatarUploadState());
  }

  /// Reset state to initial
  void resetState() {
    emit(const AvatarUploadState());
  }
}
