part of 'avatar_upload_cubit.dart';

enum AvatarUploadStatus { initial, loading, success, failure }

extension AvatarUploadStatusX on AvatarUploadStatus {
  bool get isInitial => this == AvatarUploadStatus.initial;
  bool get isLoading => this == AvatarUploadStatus.loading;
  bool get isSuccess => this == AvatarUploadStatus.success;
  bool get isFailure => this == AvatarUploadStatus.failure;
}

final class AvatarUploadState extends Equatable {
  final AvatarUploadStatus status;
  final String? errorMessage;
  final String? localImagePath;
  final String? uploadedImageUrl;
  final double uploadProgress;

  const AvatarUploadState({
    this.status = AvatarUploadStatus.initial,
    this.errorMessage,
    this.localImagePath,
    this.uploadedImageUrl,
    this.uploadProgress = 0.0,
  });

  AvatarUploadState copyWith({
    AvatarUploadStatus? status,
    String? errorMessage,
    String? localImagePath,
    String? uploadedImageUrl,
    double? uploadProgress,
  }) {
    return AvatarUploadState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      localImagePath: localImagePath ?? this.localImagePath,
      uploadedImageUrl: uploadedImageUrl ?? this.uploadedImageUrl,
      uploadProgress: uploadProgress ?? this.uploadProgress,
    );
  }

  @override
  List<Object?> get props => [
    status,
    errorMessage,
    localImagePath,
    uploadedImageUrl,
    uploadProgress,
  ];
}
