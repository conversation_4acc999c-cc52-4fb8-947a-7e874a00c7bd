import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/core/repository/coin_market_repository.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/model/crypto/crypto.dart';
import 'package:toii_social/screen/new_wallet/manager/utils/app_utils.dart';

import 'crypto/saved_crypto.dart';

part 'wallet_state.dart';

const keySelectedChain = 'selected_chain';
final tokenChainKey = "KEY_TOKENCHAIN";
final listTokenKey = "KEY_LISTTOKEN";

class WalletCubit extends Cubit<WalletState> {
  final ChainRepository _chainRepository = GetIt.instance<ChainRepository>();
  // final ChainApiRepository _chainApiRepository =
  //     GetIt.instance<ChainApiRepository>();
  final CoinMarketRepository _coinMarketRepository =
      GetIt.instance<CoinMarketRepository>();

  String _walletAddress = "";
  //List<ChainModel> listChain = [];
  WalletCubit() : super(WalletState()) {
    // initData();
  }

  void getToken({required String walletAddress}) async {
    _walletAddress = walletAddress;
    _findNodeUrl();
    // final currentActiveChain = state.currentActiveChain;
    // final tokens = await _getTokenLocalByChain();
  }

  void _findNodeUrl() async {
    final priceAll = await _coinMarketRepository.getListingLatest();
    final lists = await SavedCrypto.instance.getSavedCrypto(_walletAddress);

    final List<Asset> assets = [];
    for (var element in lists) {
      String nodeUrl = '';
      nodeUrl = await getAvailableEthRpc(element.rpcUrls ?? []);
      final crypto = element.copyWith(rpcValid: nodeUrl);

      final itemCurrency = priceAll.token.firstWhereOrNull(
        (element) =>
            element.symbol?.toLowerCase() == crypto.symbol.toLowerCase(),
      );
      if (element.isNative) {
        final nativeBalance = await _chainRepository.fetchBalanceFromChain(
          _walletAddress,
          crypto,
        );
        final asset = Asset(
          crypto: crypto.copyWith(idLogo: itemCurrency?.id ?? -1),
          balanceCrypto: nativeBalance,
          cryptoPrice: itemCurrency?.quoteModel?.usd?.price ?? 0.0,
          cryptoTrendPercent: "",
          balanceUsd: (itemCurrency?.quoteModel?.usd?.price ?? 0.0) * nativeBalance,
        );
        assets.add(asset);
      }
    }
      var sum = assets.fold(
      0.0,
      (sum, next) => sum + ((next.balanceUsd ?? 0)),
    );
    emit(state.copyWith(tokens: assets, totalBalance: sum));
  }

  // Future<List<TokenItemModel>> _getTokenLocalByChain() async {
  //   try {
  //     listChain = await _chainApiRepository.getAllChain();
  //     final result = await _coinMarketRepository.getListingLatest();
  //     SharedPref().clearKey(listTokenKey);
  //     final listTokenStr = SharedPref.getString(listTokenKey);
  //     List<dynamic> tokenJson =
  //         listTokenStr.isEmpty ? [] : json.decode(listTokenStr);
  //     List<TokenItemModel> lists = [];
  //     if (tokenJson.isEmpty) {
  //       for (var chain in listChain) {
  //         final item = TokenItemModel(
  //           chainId: chain.chainId,
  //           address: "",
  //           symbol: chain.nativeSymbol,
  //           name: chain.name,
  //           isHidden: false,
  //           isNative: true,
  //           chainName:
  //               chain.shortName.isNotEmpty ? chain.shortName : chain.name,
  //         );
  //         final nativeBalance = await _chainRepository.fetchBalanceFromChain(
  //           _walletAddress,
  //           chain,
  //         );
  //         item.balance = nativeBalance;

  //         final itemCurrency = result.token.firstWhereOrNull(
  //           (element) =>
  //               element.symbol?.toLowerCase() == item.symbol?.toLowerCase(),
  //         );
  //         if (itemCurrency != null) {
  //           item.price = itemCurrency.quoteModel?.usd?.price;
  //           item.idLogo = itemCurrency.id!;
  //         }

  //         lists.add(item);
  //       }
  //       String listTokens = jsonEncode(lists);
  //       SharedPref.setString(listTokenKey, listTokens);
  //     } else {
  //       for (var token in tokenJson) {
  //         final item = TokenItemModel.fromJson(token);
  //         final chain =
  //             listChain
  //                 .where((chain) => chain.chainId == item.chainId)
  //                 .firstOrNull;
  //         if (chain != null) {
  //           final nativeBalance = await _chainRepository.fetchBalanceFromChain(
  //             _walletAddress,
  //             chain,
  //           );
  //           item.balance = nativeBalance;
  //         }

  //         lists.add(item);
  //       }
  //     }
  //     return lists;
  //   } catch (e) {
  //     SharedPref().clearKey(listTokenKey);
  //     // _getTokenLocalByChain();
  //     log(e.toString());
  //     return [];
  //   }

  //   //   final item = TokenItemModel(
  //   //     chainId: chain.chainId,
  //   //     address: element.value['address'],
  //   //     symbol: element.value['symbol'],
  //   //     name: element.value['name'],
  //   //     isHidden: element.value['isHidden'] ?? false,
  //   //     isNative: element.value['isNative'] ?? false,
  //   //   );
  //   //   final balance = await _chainRepository.fetchBalanceOfToken(
  //   //     _walletAddress,
  //   //     item.address ?? "",
  //   //     state.currentActiveChain,
  //   //   );
  //   //   item.balance = balance;
  //   //   lists.add(item);
  //   // }
  // }

  // // for (var item in lists) {
  // //   //   if (item.symbol == state.currentActiveChain.symbol) {
  // //   final nativeBalance = await _chainRepository.fetchBalanceFromChain(
  // //     _walletAddress,
  // //     state.currentActiveChain,
  // //   );
  // //   item.balance = nativeBalance;
  // //   // } else {
  // //   //   final balance = await _chainRepository.fetchBalanceOfToken(
  // //   //     _walletAddress,
  // //   //     item.address ?? "",
  // //   //     state.currentActiveChain,
  // //   //   );
  // //   //   item.balance = balance;
  // //   // }
  // // }
}
