import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'follower_state.dart';

class FollowerCubit extends Cubit<FollowerState> {
  FollowerCubit() : super(const FollowerState());

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  void getFollowers() async {
    try {
      emit(state.copyWith(status: FollowerStatus.loading));

      // Use new method to get followers with relationship structure
      final result = await _userRepository.getFollowersNew();

      // Convert to FollowerListModel using helper method
      final followerListModel = result.data.toFollowerListModel();

      // If API returns empty data, use empty list
      if (followerListModel.followers.isEmpty) {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            followers: followerListModel.followers,
            totalFollowers: followerListModel.followers.length,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            followers: followerListModel.followers,
            totalFollowers: followerListModel.total,
          ),
        );
      }
    } catch (fallbackError) {
      // If both methods fail, use empty list and show error
      emit(
        state.copyWith(
          status: FollowerStatus.success, // Use success with empty data
          followers: [],
          totalFollowers: 0,
          errorMessage: "API error: ${fallbackError.toString()}",
        ),
      );
    }
  }

  void getFollowing({String? userId, bool isMyAccount = false}) async {
    try {
      emit(state.copyWith(status: FollowerStatus.loading));

      // Use new method to get following with relationship structure
      final result =
          isMyAccount
              ? await _userRepository.getFollowingNew()
              : await _userRepository.getUserFollowingNew(userId ?? '');

      // Convert to FollowingListModel using helper method
      final followingListModel = result.data.toFollowingListModel();

      // If API returns empty data, use empty list
      if (followingListModel.following.isEmpty) {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            following: [],
            totalFollowing: 0,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FollowerStatus.success,
            following: followingListModel.following,
            totalFollowing: followingListModel.total,
          ),
        );
      }
    } catch (fallbackError) {
      // If both methods fail, use empty list and show error
      emit(
        state.copyWith(
          status: FollowerStatus.success, // Use success with empty data
          following: [],
          totalFollowing: 0,
          errorMessage: "API error: ${fallbackError.toString()}",
        ),
      );
    }
  }

  void refreshFollowers() {
    getFollowers();
  }

  void refreshFollowing() {
    getFollowing();
  }
}
