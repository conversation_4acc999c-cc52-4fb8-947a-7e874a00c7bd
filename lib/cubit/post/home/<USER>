import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/model/post/post_model.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit() : super(const HomeState());

  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();
  int limit = 100;
  void getUserFeed({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        emit(
          state.copyWith(
            status: HomeStatus.loading,
            posts: [],
            canLoadMore: true,
          ),
        );
      } else {
        emit(state.copyWith(status: HomeStatus.loading));
      }
      emit(state.copyWith(status: HomeStatus.loading));

      final result = await _socialRepository.getUserFeed(
        state.posts.lastOrNull?.id,
        limit,
      );
      final data = [...state.posts, ...result.data.posts];
      if (result.data.posts.length < limit) {
        emit(
          state.copyWith(
            status: HomeStatus.success,
            posts: data,
            canLoadMore: false,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: HomeStatus.success,
            posts: result.data.posts,
            canLoadMore: true,
          ),
        );
      }
    } on DioException catch (e) {
      emit(
        state.copyWith(status: HomeStatus.failure, errorMessage: e.toString()),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: HomeStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void addPost(PostModel post) {
    final List<PostModel> updatedPosts = List.from(state.posts)
      ..insert(0, post);
    emit(state.copyWith(posts: updatedPosts));
  }

  void updatePost(PostModel updatedPost) {
    final List<PostModel> updatedPosts =
        state.posts
            .map((post) => post.id == updatedPost.id ? updatedPost : post)
            .toList();
    emit(state.copyWith(posts: updatedPosts));
  }

  void updatePostLikeCount(String postId, int newLikeCount, bool isLiked) {
    final updatedPosts =
        state.posts.map((post) {
          if (post.id != postId) return post;

          // Update reactions count for 'love' type
          final updatedReactions =
              post.reactions?.reactions?.map((reaction) {
                return reaction.type?.toLowerCase() == 'love'
                    ? reaction.copyWith(count: newLikeCount)
                    : reaction;
              }).toList() ??
              [];

          // Update user reactions
          final currentUserReactions = post.reactions?.userReactions ?? [];
          final updatedUserReactions =
              isLiked
                  ? [...currentUserReactions, 'love']
                  : currentUserReactions.where((r) => r != 'love').toList();

          return post.copyWith(
            reactions: post.reactions?.copyWith(
              reactions: updatedReactions,
              userReactions: updatedUserReactions.cast<String>(),
            ),
          );
        }).toList();

    emit(state.copyWith(posts: updatedPosts));
  }
}
