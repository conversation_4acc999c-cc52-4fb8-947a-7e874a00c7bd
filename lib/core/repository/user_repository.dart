import 'dart:io';

import 'package:get_it/get_it.dart';
import 'package:toii_social/core/service/user_service.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/model/base/base_response.dart';
import 'package:toii_social/model/follower/follower_model.dart';
import 'package:toii_social/model/user/update_user_request_model.dart';
import 'package:toii_social/model/user/user_model.dart';

abstract class UserRepository {
  Future<BaseResponse<UserModel>> getUserStats(String userId);

  Future<BaseResponse<FollowingListModel>> getFollowing();

  // New methods for API response with relationship structure
  Future<FollowerApiResponseModel> getFollowersNew();
  Future<FollowerApiResponseModel> getFollowingNew();
  Future<FollowerApiResponseModel> getUserFollowingNew(String userId);

  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  );

  Future<void> uploadAvatar(File avatarFile);
}

class UserRepositoryImpl extends UserRepository {
  final UserService userService;

  UserRepositoryImpl({required this.userService});

  @override
  Future<BaseResponse<UserModel>> getUserStats(String userId) async {
    try {
      return await userService.getUserStats(userId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BaseResponse<FollowingListModel>> getFollowing() {
    return userService.getFollowing();
  }

  // New methods implementation
  @override
  Future<FollowerApiResponseModel> getFollowersNew() {
    return userService.getFollowersNew();
  }

  @override
  Future<FollowerApiResponseModel> getFollowingNew() {
    return userService.getFollowingNew();
  }

  @override
  Future<FollowerApiResponseModel> getUserFollowingNew(String userId) {
    return userService.getUserFollowingNew(userId);
  }

  @override
  Future<BaseResponse<UserModel>> updateUser(
    String userId,
    UpdateUserRequestModel request,
  ) async {
    try {
      return await userService.updateUser(userId, request);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> uploadAvatar(File avatarFile) async {
    try {
      // Call API POST /users/avatar with multipart/form-data
      await userService.uploadAvatar(avatarFile);

      // If upload successful, refresh profile
      GetIt.instance<ProfileCubit>().getProfile();
    } catch (e) {
      // If failed, throw exception with error message
      throw Exception('Failed to upload avatar: ${e.toString()}');
    }
  }
}
