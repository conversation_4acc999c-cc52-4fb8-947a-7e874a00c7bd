import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AvatarSelectionBottomSheet extends StatelessWidget {
  final VoidCallback onGalleryTap;
  final VoidCallback onCameraTap;
  final VoidCallback onAIGenerateTap;

  const AvatarSelectionBottomSheet({
    super.key,
    required this.onGalleryTap,
    required this.onCameraTap,
    required this.onAIGenerateTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: themeData.white900,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with drag indicator
            Container(
              width: 32,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 24),
              decoration: BoxDecoration(
                color: themeData.neutral300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Upload from Gallery option
            _buildAvatarOption(
              context: context,
              icon: Assets.icons.icPicture.svg(
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  themeData.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: 'Upload',
              onTap: () {
                Navigator.pop(context);
                onGalleryTap();
              },
            ),

            // Take Photo option
            _buildAvatarOption(
              context: context,
              icon: Icon(
                Icons.camera_alt,
                size: 24,
                color: themeData.textSecondary,
              ),
              title: 'Take Photo',
              onTap: () {
                Navigator.pop(context);
                onCameraTap();
              },
            ),

            // Generate with AI option (Coming soon)
            _buildAvatarOption(
              context: context,
              icon: Assets.icons.icGenAi.svg(
                width: 24,
                height: 24,
                colorFilter: ColorFilter.mode(
                  themeData.textSecondary,
                  BlendMode.srcIn,
                ),
              ),
              title: 'Generate with AI',
              onTap: () {
                Navigator.pop(context);
                onAIGenerateTap();
              },
            ),

            // Bottom padding for safe area
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarOption({
    required BuildContext context,
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final themeData = Theme.of(context);
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper function to show the avatar selection bottom sheet
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onGalleryTap,
    required VoidCallback onCameraTap,
    required VoidCallback onAIGenerateTap,
  }) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => AvatarSelectionBottomSheet(
        onGalleryTap: onGalleryTap,
        onCameraTap: onCameraTap,
        onAIGenerateTap: onAIGenerateTap,
      ),
    );
  }
}
