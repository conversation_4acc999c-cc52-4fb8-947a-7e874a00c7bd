import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/constant.dart';
import 'package:toii_social/cubit/auth/logout/logout_cubit.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/setting_profile/widget/logout_dialog.dart';
import 'package:toii_social/screen/user_profile/setting_profile/widget/setting_list_item.dart';
import 'package:toii_social/utils/url_luncher/url_luncher.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class SettingProfileScreen extends StatefulWidget {
  const SettingProfileScreen({super.key});

  @override
  State<SettingProfileScreen> createState() => _SettingProfileScreenState();
}

class _SettingProfileScreenState extends State<SettingProfileScreen> {
  late ProfileCubit _profileCubit;
  late LogoutCubit _logoutCubit;

  @override
  void initState() {
    super.initState();
    _profileCubit = GetIt.instance<ProfileCubit>();
    _logoutCubit = LogoutCubit();

    // Load profile data if not already loaded
    if (_profileCubit.state.status.isInitial) {
      _profileCubit.getProfile();
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(title: 'Setting', centerTitle: false),
      body: Column(
        children: [
          // User Profile Section
          BlocProvider.value(
            value: _profileCubit,
            child: BlocBuilder<ProfileCubit, ProfileState>(
              builder: (context, state) {
                return InkWell(
                  onTap: () {
                    context.push(RouterEnums.accountInfo.routeName);
                  },
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: themeData.neutral100,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        // Avatar
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: ClipOval(
                            child:
                                state.userModel?.avatar?.isNotEmpty == true
                                    ? Image.network(
                                      state.userModel!.avatar!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Assets.images.avatarSample.image(
                                          fit: BoxFit.cover,
                                        );
                                      },
                                    )
                                    : Assets.images.avatarSample.image(
                                      fit: BoxFit.cover,
                                    ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // User Info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                state.userModel?.fullName ??
                                    state.userModel?.firstName ??
                                    state.userModel?.username ??
                                    '-',
                                style: titleMedium.copyWith(
                                  color: themeData.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                state.userModel?.username ?? '-',
                                style: labelMedium.copyWith(
                                  color: themeData.textSecondary,
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Arrow
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: Assets.icons.icArrowRightFigma.svg(
                            colorFilter: ColorFilter.mode(
                              themeData.textSecondary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          // Settings List
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: themeData.neutral50,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  SettingListItem(
                    icon: Assets.icons.icNotificationBing.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Notifications',
                    onTap: () {
                      // TODO: Navigate to notifications settings
                    },
                  ),

                  SettingListItem(
                    icon: Assets.icons.icBlock.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Blocked',
                    onTap: () {
                      // TODO: Navigate to blocked users
                    },
                  ),

                  SettingListItem(
                    icon: Assets.icons.icShieldSecurity.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Account privacy',
                    onTap: () {
                      // TODO: Navigate to account privacy
                    },
                  ),

                  SettingListItem(
                    icon: Assets.icons.icLanguageSquare.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Language',
                    onTap: () {
                      // TODO: Navigate to language settings
                    },
                  ),

                  SettingListItem(
                    icon: Assets.icons.icTheme.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Theme',
                    onTap: () {
                      // TODO: Navigate to theme settings
                    },
                  ),
                  _buildDivider(themeData),
                  SettingListItem(
                    icon: Assets.icons.icLock2Svg.svg(
                      colorFilter: ColorFilter.mode(
                        themeData.textSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    title: 'Privacy Policy',
                    onTap: () {
                      launchUrlCustom(Constant.baseUrlPrivacy);
                    },
                  ),

                  // SettingListItem(
                  //   icon: Assets.icons.icNote.svg(
                  //     colorFilter: ColorFilter.mode(
                  //       themeData.textSecondary,
                  //       BlendMode.srcIn,
                  //     ),
                  //   ),
                  //   title: 'Terms of Service',
                  //   onTap: () {
                  //     // TODO: Navigate to terms of service
                  //   },
                  // ),
                  BlocProvider.value(
                    value: _logoutCubit,
                    child: SettingListItem(
                      icon: Assets.icons.icLogout.svg(
                        colorFilter: ColorFilter.mode(
                          themeData.textSecondary,
                          BlendMode.srcIn,
                        ),
                      ),
                      title: 'Log out',
                      onTap: () {
                        LogoutDialog.show(context, _logoutCubit);
                      },
                      showArrow: false,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDivider(ThemeData themeData) {
    return Container(
      height: 1,
      margin: const EdgeInsets.only(left: 56),
      color: themeData.neutral200,
    );
  }
}
