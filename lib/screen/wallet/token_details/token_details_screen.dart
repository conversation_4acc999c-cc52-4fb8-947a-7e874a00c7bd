import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/wallet/widgets/crypto_logo_network.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

// Transaction models
enum TransactionType { sent, received }

enum TransactionStatus { completed, pending, failed }

class TransactionItem {
  final TransactionType type;
  final String amount;
  final String currency;
  final String usdValue;
  final String fromAddress;
  final DateTime date;
  final TransactionStatus status;

  TransactionItem({
    required this.type,
    required this.amount,
    required this.currency,
    required this.usdValue,
    required this.fromAddress,
    required this.date,
    required this.status,
  });
}

class TokenDetailsScreen extends StatefulWidget {
  final Asset token;

  const TokenDetailsScreen({super.key, required this.token});

  @override
  State<TokenDetailsScreen> createState() => _TokenDetailsScreenState();
}

class _TokenDetailsScreenState extends State<TokenDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // final box = Hive.box<Transaction>('transactions');
    // for (var receipt in box.values) {
    //   print(receipt.transactionHash);
    // }
  }

  bool _isBalanceVisible = true;

  // Mock transaction data - replace with actual data from your backend/Hive
  List<TransactionItem> get _transactions => [
    TransactionItem(
      type: TransactionType.received,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 30),
      status: TransactionStatus.completed,
    ),
    TransactionItem(
      type: TransactionType.received,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 30),
      status: TransactionStatus.completed,
    ),
    TransactionItem(
      type: TransactionType.sent,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 25),
      status: TransactionStatus.completed,
    ),
    TransactionItem(
      type: TransactionType.sent,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 24),
      status: TransactionStatus.completed,
    ),
    TransactionItem(
      type: TransactionType.sent,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 20),
      status: TransactionStatus.pending,
    ),
    TransactionItem(
      type: TransactionType.sent,
      amount: '0.0034',
      currency: 'TOII',
      usdValue: '\$145.78',
      fromAddress: 'Toiinbsbvdv124325hghg...',
      date: DateTime(2025, 7, 20),
      status: TransactionStatus.failed,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      showLeading: true,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CryptoLogoNetwork(
            crypto: widget.token.crypto,
            id: widget.token.crypto.idLogo.toString(),
            size: 24,
          ),

          const SizedBox(width: 8),
          Text(
            widget.token.crypto.symbol.toUpperCase(),
            style: titleLarge.copyColor(themeData.neutral800),
          ),
        ],
      ),
      body: Column(
        children: [
          // Balance Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Balance',
                  style: titleMedium.copyColor(themeData.neutral800),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      _isBalanceVisible
                          ? "${(widget.token.balanceCrypto ?? 0)}"
                          : '* * * *',
                      style: headlineLarge.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(width: 12),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isBalanceVisible = !_isBalanceVisible;
                        });
                      },
                      child:
                          _isBalanceVisible
                              ? SvgPicture.asset(
                                Assets.icons.icShowEye.path,
                                colorFilter: ColorFilter.mode(
                                  themeData.black400,
                                  BlendMode.srcIn,
                                ),
                              )
                              : SvgPicture.asset(
                                Assets.icons.icHideEye.path,
                                colorFilter: ColorFilter.mode(
                                  themeData.black400,
                                  BlendMode.srcIn,
                                ),
                              ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _isBalanceVisible ? '≈ \$ ${0.0}' : '≈ \$••••',
                  style: labelLarge.copyColor(themeData.neutral400),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                path: Assets.icons.icSend.path,
                label: 'Send',
                onTap: () {
                  context.push(
                    RouterEnums.sendToken.routeName,
                    extra: widget.token,
                  );
                },
              ),
              _buildActionButton(
                path: Assets.icons.icReceive.path,
                label: 'Receive',
                onTap: () {
                  context.push(
                    RouterEnums.receiveToken.routeName,
                    extra: widget.token.crypto,
                    // extra: context.read<ProfileCubit>().state.userModel?.walletAddress,
                  );
                },
              ),
              _buildActionButton(
                path: Assets.icons.icSwap.path,
                label: 'Swap',
                onTap: () => {},
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Token History Section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    'Token History',
                    style: titleMedium.copyColor(themeData.neutral800),
                  ),
                ),

                Expanded(
                  child: ListView.separated(
                    padding: const EdgeInsets.all(20),
                    itemCount: _transactions.length,
                    separatorBuilder:
                        (context, index) => const SizedBox(height: 16),
                    itemBuilder: (context, index) {
                      final transaction = _transactions[index];
                      return _buildTransactionItem(context, transaction);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String path,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.neutral100,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Center(child: SvgPicture.asset(path)),
          ),
          const SizedBox(height: 8),
          Text(label, style: bodyMedium.copyColor(themeData.neutral800)),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(
    BuildContext context,
    TransactionItem transaction,
  ) {
    final isReceived = transaction.type == TransactionType.received;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeData.neutral50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: themeData.neutral200),
      ),
      child: Row(
        children: [
          // Transaction type icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color:
                  isReceived
                      ? themeData.primaryGreen100
                      : themeData.red500.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isReceived ? Icons.arrow_downward : Icons.arrow_upward,
              color: isReceived ? themeData.primaryGreen600 : themeData.red500,
              size: 20,
            ),
          ),

          const SizedBox(width: 12),

          // Transaction details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      isReceived ? 'Received' : 'Sent',
                      style: bodyMedium.copyColor(themeData.neutral800),
                    ),
                    const Spacer(),
                    Text(
                      '${isReceived ? '+' : '-'}${transaction.amount} ${transaction.currency}',
                      style: bodyMedium.copyColor(
                        isReceived
                            ? themeData.primaryGreen600
                            : themeData.neutral800,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      DateFormat('MMM dd, yyyy').format(transaction.date),
                      style: bodySmall.copyColor(themeData.neutral400),
                    ),
                    const Spacer(),
                    Text(
                      '\$${transaction.usdValue}',
                      style: bodySmall.copyColor(themeData.neutral400),
                    ),
                  ],
                ),
                if (transaction.status != TransactionStatus.completed) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color:
                          transaction.status == TransactionStatus.pending
                              ? themeData.primaryGreen100
                              : themeData.red500.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      transaction.status == TransactionStatus.pending
                          ? 'Pending'
                          : 'Failed',
                      style: bodySmall.copyColor(
                        transaction.status == TransactionStatus.pending
                            ? themeData.primaryGreen600
                            : themeData.red500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
