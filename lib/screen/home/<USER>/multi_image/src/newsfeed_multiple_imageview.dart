import 'package:flutter/material.dart';
import 'package:flutter_image_slideshow/flutter_image_slideshow.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';

import 'multiple_image_view.dart';
import 'smart_image.dart';

class NewsfeedMultipleImageView extends StatelessWidget {
  final List<MediaDetailsModel> imageUrls;
  final double marginLeft;
  final double marginTop;
  final double marginRight;
  final double marginBottom;
  final PostModel? post;

  const NewsfeedMultipleImageView({
    super.key,
    this.marginLeft = 0,
    this.marginTop = 0,
    this.marginRight = 0,
    this.marginBottom = 0,
    required this.imageUrls,
    this.post,
  });

  @override
  Widget build(BuildContext context) {
    final images =
        imageUrls.map((e) {
          //if (e.imageVariants.isEmpty) {
          return e.cdnUrl ?? "";
          // } else {
          //  return e.imageVariants.first.cdnUrl ?? '';
          // }
        }).toList();

    if (imageUrls.length == 1) {
      return GestureDetector(
        onTap: () {
          if (post != null) {
            context.push(
              RouterEnums.detailPreviewImg.routeName,
              extra: DetailPreviewImgArg(
                images: images,
                initialIndex: 0,
                post: post,
              ),
            );
          }
        },
        child: SizedBox(
          width: double.infinity,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.width * 16 / 9,
            ),
            child: Hero(
              tag: '${post?.id ?? 'single'}_${imageUrls.first}_0',
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: SmartImage(
                  images.first,
                  fit: BoxFit.fitWidth,
                  isPost: true,
                ),
              ),
            ),
          ),
        ),
      );
    }
    return LayoutBuilder(
      builder:
          (context, costraints) => Container(
            width: costraints.maxWidth,
            height: costraints.maxWidth,
            margin: EdgeInsets.fromLTRB(
              marginLeft,
              marginTop,
              marginRight,
              marginBottom,
            ),
            child: MultipleImageView(imageUrls: images, post: post),
          ),
    );
  }
}

class ImageViewer extends StatelessWidget {
  final List<String> imageUrls;
  const ImageViewer({super.key, required this.imageUrls});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Container(
          // width: MediaQuery.of(context).size.width,
          // height: MediaQuery.of(context).size.height,
          color: Colors.black,
          child: SafeArea(
            top: false,
            left: false,
            right: false,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close, color: Colors.white, size: 30),
                ),
                Expanded(
                  child: ImageSlideshow(
                    initialPage: 0,
                    indicatorColor: Colors.red,
                    indicatorBackgroundColor: Colors.grey,
                    isLoop: imageUrls.length > 1,
                    children:
                        imageUrls
                            .map(
                              (e) => ClipRect(
                                child: SmartImage(
                                  e,
                                  fit: BoxFit.contain,
                                  isPost: true,
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
