import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/multi_image/src/newsfeed_multiple_imageview.dart';
import 'package:toii_social/widget/colors/colors.dart';

class PostImageGallery extends StatelessWidget {
  final bool isRepost;
  final PostModel post;
  const PostImageGallery({
    super.key,
    required this.post,
    this.isRepost = false,
  });

  @override
  Widget build(BuildContext context) {
    final images = post.mediaDetails;
    if (images.isEmpty) {
      return const SizedBox(height: 16);
    }

    return Stack(
      children: [
        if (!isRepost)
          Container(
            height: 60,
            decoration: BoxDecoration(
              color: themeData.black50,
              // borderRadius: BorderRadius.only(
              //   topLeft: Radius.circular(16),
              //   topRight: Radius.circular(16),
              // ),
            ),
          ),
        NewsfeedMultipleImageView(
          imageUrls: images,
          marginLeft: 0.0,
          marginRight: 0.0,
          marginBottom: 0.0,
          marginTop: 0.0,
          post: post,
        ),
      ],
    );
  }
}
